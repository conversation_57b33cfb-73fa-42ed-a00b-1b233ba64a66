import { UseNumberInputParameters, UseNumberInputReturnValue } from './useNumberInput.types';
export declare function getInputValueAsString(v: string): string;
/**
 *
 * Demos:
 *
 * - [Number Input](https://mui.com/base-ui/react-number-input/#hook)
 *
 * API:
 *
 * - [useNumberInput API](https://mui.com/base-ui/react-number-input/hooks-api/#use-number-input)
 */
export declare function useNumberInput(parameters: UseNumberInputParameters): UseNumberInputReturnValue;
